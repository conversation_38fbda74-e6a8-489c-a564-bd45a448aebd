/**
 * Monolith Design Co. - Main Stylesheet
 * Following the 8-point grid system and design specifications
 */

/* CSS Variables for Design System */
:root {
    /* Colors */
    --primary-color: #1A1A1A;
    --secondary-color: #F5F5F5;
    --accent-color: #E67E22;
    --text-color: #333333;
    --white: #FFFFFF;
    --black: #000000;
    
    /* Grid System - 8-point grid */
    --spacing-xs: 8px;
    --spacing-sm: 16px;
    --spacing-md: 24px;
    --spacing-lg: 32px;
    --spacing-xl: 48px;
    --spacing-xxl: 64px;
    --spacing-xxxl: 96px;
    
    /* Typography */
    --font-heading: 'Montserrat', sans-serif;
    --font-body: 'Lato', sans-serif;
    
    /* Container */
    --container-max-width: 1200px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Shadows */
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);
    
    /* Border Radius */
    --radius-small: 4px;
    --radius-medium: 8px;
    --radius-large: 16px;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-body);
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
}

h1 {
    font-size: 3rem;
    font-weight: 700;
}

h2 {
    font-size: 2.5rem;
}

h3 {
    font-size: 2rem;
}

h4 {
    font-size: 1.5rem;
}

h5 {
    font-size: 1.25rem;
}

h6 {
    font-size: 1rem;
}

p {
    margin-bottom: var(--spacing-sm);
}

a {
    color: var(--accent-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: #d35400;
}

/* Container and Layout */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.section-padding {
    padding: var(--spacing-xxxl) 0;
}

.text-center {
    text-align: center;
}

.text-white {
    color: var(--white);
}

/* Background Utilities */
.bg-light {
    background-color: var(--secondary-color);
}

.bg-dark {
    background-color: var(--primary-color);
}

.bg-accent {
    background-color: var(--accent-color);
}

/* Section Headers */
.section-header {
    margin-bottom: var(--spacing-xxl);
}

.section-title {
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.section-subtitle {
    font-size: 1.125rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.bg-dark .section-title,
.bg-accent .section-title {
    color: var(--white);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-family: var(--font-heading);
    font-weight: 600;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: 2px solid;
    border-radius: var(--radius-small);
    cursor: pointer;
    transition: all var(--transition-medium);
    text-decoration: none;
}

.btn-primary {
    background-color: var(--accent-color);
    color: var(--white);
    border-color: var(--accent-color);
}

.btn-primary:hover {
    background-color: #d35400;
    border-color: #d35400;
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-outline {
    background-color: transparent;
    color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-outline:hover {
    background-color: var(--accent-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-white {
    background-color: var(--white);
    color: var(--accent-color);
    border-color: var(--white);
}

.btn-white:hover {
    background-color: transparent;
    color: var(--white);
    border-color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-small {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 0.875rem;
}

/* Header Styles */
.site-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: all var(--transition-medium);
}

.site-header.scrolled {
    padding: var(--spacing-xs) 0;
    background-color: rgba(255, 255, 255, 0.98);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) 0;
}

.logo-img {
    height: 40px;
    width: auto;
}

/* Navigation */
.main-navigation {
    display: flex;
    align-items: center;
}

.nav-menu {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-left: var(--spacing-lg);
    position: relative;
}

.nav-link {
    font-family: var(--font-heading);
    font-weight: 500;
    color: var(--text-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
    padding: var(--spacing-xs) 0;
    position: relative;
    transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
    color: var(--accent-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--accent-color);
    transition: width var(--transition-fast);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Dropdown Menu */
.has-dropdown .dropdown-arrow {
    margin-left: var(--spacing-xs);
    font-size: 0.75rem;
    transition: transform var(--transition-fast);
}

.has-dropdown:hover .dropdown-arrow {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--white);
    border-radius: var(--radius-medium);
    box-shadow: var(--shadow-medium);
    padding: var(--spacing-sm) 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-medium);
    z-index: 100;
}

.has-dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
}

.dropdown-link {
    display: block;
    padding: var(--spacing-xs) var(--spacing-md);
    color: var(--text-color);
    font-family: var(--font-body);
    font-weight: 400;
    text-transform: none;
    letter-spacing: normal;
    font-size: 0.875rem;
    transition: background-color var(--transition-fast);
}

.dropdown-link:hover {
    background-color: var(--secondary-color);
    color: var(--accent-color);
}

.dropdown-divider {
    height: 1px;
    background-color: #eee;
    margin: var(--spacing-xs) 0;
}

.view-all {
    font-weight: 600;
    color: var(--accent-color);
}

/* Mobile Menu */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: var(--spacing-xs);
}

.hamburger-line {
    width: 24px;
    height: 2px;
    background-color: var(--text-color);
    margin: 2px 0;
    transition: all var(--transition-fast);
}

.mobile-navigation {
    display: none;
    position: fixed;
    top: 72px;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--white);
    z-index: 999;
    overflow-y: auto;
}

.mobile-nav-content {
    padding: var(--spacing-lg);
}

.mobile-nav-menu {
    list-style: none;
}

.mobile-nav-item {
    margin-bottom: var(--spacing-sm);
}

.mobile-nav-link {
    display: block;
    padding: var(--spacing-sm) 0;
    font-family: var(--font-heading);
    font-weight: 500;
    color: var(--text-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid #eee;
}

/* Hero Slider */
.hero-slider {
    position: relative;
    height: 100vh;
    overflow: hidden;
}

.slider-container {
    position: relative;
    height: 100%;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.2) 100%);
}

.slide-content {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    text-align: center;
    z-index: 2;
}

.slide-title {
    color: var(--white);
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.slide-subtitle {
    color: var(--white);
    font-size: 1.25rem;
    margin-bottom: var(--spacing-xl);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Slider Navigation */
.slider-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 3;
}

.slider-prev {
    left: var(--spacing-lg);
}

.slider-next {
    right: var(--spacing-lg);
}

.slider-prev,
.slider-next {
    position: absolute;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 1.5rem;
    color: var(--text-color);
    cursor: pointer;
    transition: all var(--transition-medium);
}

.slider-prev:hover,
.slider-next:hover {
    background: var(--white);
    transform: scale(1.1);
    box-shadow: var(--shadow-medium);
}

.slider-dots {
    position: absolute;
    bottom: var(--spacing-lg);
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: var(--spacing-sm);
    z-index: 3;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    border: none;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.dot.active,
.dot:hover {
    background: var(--white);
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.service-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-large);
    text-align: center;
    transition: all var(--transition-medium);
    border: 1px solid #eee;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy);
}

.service-icon {
    margin-bottom: var(--spacing-md);
}

.service-icon img {
    width: 64px;
    height: 64px;
    transition: all var(--transition-medium);
}

.service-card:hover .service-icon img {
    filter: brightness(0) saturate(100%) invert(56%) sepia(78%) saturate(1375%) hue-rotate(21deg) brightness(95%) contrast(87%);
}

.service-title {
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.service-description {
    margin-bottom: var(--spacing-md);
    color: #666;
}

.service-link {
    font-family: var(--font-heading);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: var(--accent-color);
    position: relative;
}

.service-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background-color: var(--accent-color);
    transition: width var(--transition-fast);
}

.service-link:hover::after {
    width: 100%;
}

/* Projects Grid */
.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.project-card {
    position: relative;
    border-radius: var(--radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-medium);
}

.project-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-heavy);
}

.project-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-medium);
}

.project-card:hover .project-overlay {
    opacity: 1;
}

.project-info {
    text-align: center;
    color: var(--white);
}

.project-title {
    margin-bottom: var(--spacing-xs);
    color: var(--white);
}

.project-category {
    color: var(--accent-color);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: var(--spacing-md);
}

.project-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: var(--accent-color);
    color: var(--white);
    border-radius: 50%;
    font-size: 1.5rem;
    font-weight: 300;
    transition: all var(--transition-medium);
}

.project-link:hover {
    background: #d35400;
    transform: scale(1.1);
    color: var(--white);
}

/* Company Philosophy */
.philosophy-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: center;
}

.philosophy-image img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-medium);
}

.philosophy-description {
    font-size: 1.125rem;
    line-height: 1.7;
    margin-bottom: var(--spacing-xl);
    color: #555;
}

.core-values {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.value-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.value-icon img {
    width: 32px;
    height: 32px;
    filter: brightness(0) saturate(100%) invert(56%) sepia(78%) saturate(1375%) hue-rotate(21deg) brightness(95%) contrast(87%);
}

.value-text h4 {
    margin-bottom: var(--spacing-xs);
    color: var(--primary-color);
}

.value-text p {
    color: #666;
    margin: 0;
}

/* Testimonials */
.testimonials-slider {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.testimonial-slide {
    text-align: center;
    opacity: 0;
    transition: opacity var(--transition-slow);
}

.testimonial-slide.active {
    opacity: 1;
}

.testimonial-quote {
    font-size: 1.5rem;
    font-style: italic;
    color: var(--white);
    margin-bottom: var(--spacing-xl);
    line-height: 1.6;
}

.testimonial-author strong {
    display: block;
    color: var(--white);
    font-size: 1.125rem;
    margin-bottom: var(--spacing-xs);
}

.testimonial-author span {
    color: var(--accent-color);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Why Choose Us Section */
.why-choose-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.why-item {
    text-align: center;
    padding: var(--spacing-xl);
    background: var(--white);
    border-radius: var(--radius-large);
    border: 1px solid #eee;
    transition: all var(--transition-medium);
}

.why-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy);
}

.why-number {
    font-family: var(--font-heading);
    font-size: 3rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: var(--spacing-md);
    line-height: 1;
}

.why-title {
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
    font-size: 1.25rem;
}

.why-description {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* CTA Section */
.final-cta {
    text-align: center;
}

.cta-title {
    color: var(--white);
    margin-bottom: var(--spacing-sm);
}

.cta-subtitle {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.125rem;
    margin-bottom: var(--spacing-xl);
}

.section-cta {
    margin-top: var(--spacing-xl);
}

/* Footer */
.site-footer {
    background-color: var(--primary-color);
    color: var(--white);
}

.footer-content {
    padding: var(--spacing-xxxl) 0;
}

.footer-logo {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
}

.footer-logo-img {
    height: 50px;
    width: auto;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xxl);
}

.footer-heading {
    color: var(--white);
    margin-bottom: var(--spacing-md);
    font-size: 1.125rem;
}

.footer-text {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-xs);
}

.footer-link {
    color: rgba(255, 255, 255, 0.8);
    transition: color var(--transition-fast);
}

.footer-link:hover {
    color: var(--accent-color);
}

.footer-contact .contact-item {
    margin-bottom: var(--spacing-md);
}

.footer-contact strong {
    color: var(--white);
}

.footer-social {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
}

.social-links {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    border-radius: 50%;
    transition: all var(--transition-medium);
}

.social-link:hover {
    background: var(--accent-color);
    color: var(--white);
    transform: translateY(-2px);
}

.footer-separator {
    height: 1px;
    background: rgba(255, 255, 255, 0.2);
    margin-bottom: var(--spacing-lg);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: rgba(255, 255, 255, 0.7);
}

.footer-legal {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.footer-legal .separator {
    color: rgba(255, 255, 255, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    /* Mobile Navigation */
    .main-navigation {
        display: none;
    }
    
    .mobile-menu-toggle {
        display: flex;
    }
    
    /* Typography Adjustments */
    h1 {
        font-size: 2.5rem;
    }
    
    h2 {
        font-size: 2rem;
    }
    
    .slide-title {
        font-size: 2.5rem;
    }
    
    .slide-subtitle {
        font-size: 1rem;
    }
    
    /* Section Padding */
    .section-padding {
        padding: var(--spacing-xxl) 0;
    }
    
    /* Philosophy Content */
    .philosophy-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }
    
    /* Footer */
    .footer-bottom {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    /* Services Grid */
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    /* Projects Grid */
    .projects-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .slide-title {
        font-size: 2rem;
    }
    
    .section-padding {
        padding: var(--spacing-xl) 0;
    }
    
    .btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.875rem;
    }
}

/* Page Hero Styles (for About, Services, etc.) */
.page-hero {
    position: relative;
    height: 60vh;
    min-height: 400px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.hero-title {
    color: var(--white);
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    color: var(--white);
    font-size: 1.25rem;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    margin: 0 auto;
}

/* Mission Section */
.mission-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxl);
    align-items: center;
}

.mission-description {
    font-size: 1.125rem;
    line-height: 1.7;
    margin-bottom: var(--spacing-lg);
    color: #555;
}

.mission-image {
    position: relative;
}

.mission-image img {
    width: 100%;
    height: auto;
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-medium);
}

.mission-stats {
    position: absolute;
    bottom: -20px;
    right: -20px;
    display: flex;
    gap: var(--spacing-md);
}

.stat-item {
    background: var(--white);
    padding: var(--spacing-md);
    border-radius: var(--radius-medium);
    text-align: center;
    box-shadow: var(--shadow-medium);
    min-width: 100px;
}

.stat-number {
    font-family: var(--font-heading);
    font-size: 2rem;
    font-weight: 700;
    color: var(--accent-color);
    line-height: 1;
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 0.875rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Values Grid */
.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.value-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-large);
    text-align: center;
    border: 1px solid #eee;
    transition: all var(--transition-medium);
}

.value-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy);
}

.value-icon {
    margin-bottom: var(--spacing-md);
}

.value-icon img {
    width: 64px;
    height: 64px;
}

.value-title {
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
}

.value-description {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* Team Grid */
.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.team-card {
    background: var(--white);
    border-radius: var(--radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-medium);
}

.team-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy);
}

.team-image {
    width: 100%;
    height: 280px;
    overflow: hidden;
}

.team-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.team-card:hover .team-image img {
    transform: scale(1.05);
}

.team-info {
    padding: var(--spacing-xl);
    text-align: center;
}

.team-name {
    margin-bottom: var(--spacing-xs);
    color: var(--primary-color);
}

.team-position {
    color: var(--accent-color);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
    margin-bottom: var(--spacing-md);
}

.team-bio {
    color: #666;
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.team-linkedin {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: var(--accent-color);
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: color var(--transition-fast);
}

.team-linkedin:hover {
    color: #d35400;
}

/* Office Gallery */
.office-gallery {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xxl);
}

.office-image {
    border-radius: var(--radius-large);
    overflow: hidden;
    box-shadow: var(--shadow-medium);
}

.main-image {
    height: 400px;
}

.office-images-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-md);
}

.office-images-grid .office-image {
    height: 190px;
}

.office-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.office-image:hover img {
    transform: scale(1.05);
}

.office-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.feature-item {
    text-align: center;
    color: rgba(255, 255, 255, 0.9);
}

.feature-item h4 {
    color: var(--white);
    margin-bottom: var(--spacing-sm);
}

.feature-item p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

/* Loading Animation */
.loading {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Smooth Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-on-scroll {
    animation: fadeInUp 0.6s ease forwards;
}
