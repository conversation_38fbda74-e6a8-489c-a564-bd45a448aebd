# Monolith Design Co. - Professional Engineering & Architecture Website

A comprehensive PHP website theme designed specifically for engineering and architectural firms. Features clean URLs, responsive design, and a powerful admin dashboard.

## 🏗️ Features

### Design System
- **8-point grid system** for perfect visual rhythm
- **Montserrat** headings and **Lato** body text
- **Burnt orange (#E67E22)** accent color (customizable)
- **Mobile-first responsive design**
- **Clean, modern aesthetic** inspired by architectural precision

### Homepage Sections
1. **Hero Slider** - Auto-playing slides with navigation
2. **Services Overview** - Interactive service cards
3. **Featured Projects** - Portfolio showcase with hover effects
4. **Company Philosophy** - Values and principles
5. **Client Testimonials** - Auto-rotating testimonials
6. **Call-to-Action** - Contact encouragement

### Technical Features
- **Clean URLs** (no .php extensions)
- **SEO optimized** with proper meta tags
- **Secure coding** with prepared statements
- **File upload system** for images
- **Contact form** with validation
- **Admin dashboard** for content management
- **Theme customization** options

### Content Management
- Dynamic sliders with admin control
- Service pages with detailed descriptions
- Project portfolio with categories
- Team member profiles
- Blog system
- Contact form submissions
- Theme options panel

## 🚀 Installation

### Requirements
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache web server with mod_rewrite
- At least 50MB disk space

### Quick Setup
1. **Download/Extract** the files to your web server directory
2. **Configure Database** - Edit `config.php` with your database credentials
3. **Run Installer** - Visit `yoursite.com/install.php` in your browser
4. **Delete Installer** - Remove `install.php` after successful installation
5. **Access Website** - Visit your homepage

### Manual Database Setup
If the automatic installer doesn't work:
1. Create a MySQL database named `monolith_design`
2. Import the `database.sql` file
3. Update database credentials in `config.php`

## 📁 File Structure

```
monolith-design/
├── assets/
│   ├── css/
│   │   ├── style.css          # Main stylesheet
│   │   └── responsive.css     # Mobile responsive styles
│   ├── js/
│   │   └── main.js           # Interactive functionality
│   └── images/
│       └── uploads/          # User uploaded images
├── includes/
│   └── functions.php         # Core functions and database
├── templates/
│   ├── header.php           # Site header template
│   └── footer.php           # Site footer template
├── pages/                   # Individual page files
├── admin/                   # Admin dashboard (to be created)
├── config.php              # Site configuration
├── index.php               # Homepage
├── database.sql            # Database structure and data
├── install.php             # Database installer
├── .htaccess               # URL rewriting rules
└── README.md               # This file
```

## 🎨 Customization

### Theme Colors
The accent color can be changed in the admin panel or by updating:
- CSS variable `--accent-color` in `style.css`
- Database theme option `accent_color`

### Adding Content
1. **Sliders** - Add/edit through admin panel
2. **Services** - Create service pages with descriptions
3. **Projects** - Upload project images and details
4. **Team Members** - Add staff profiles
5. **Testimonials** - Client feedback management

### Images
Replace placeholder images in `/assets/images/` with:
- **Logo** - Company logo (SVG recommended)
- **Hero backgrounds** - High-quality architectural images
- **Service icons** - Custom SVG icons
- **Project photos** - Portfolio images
- **Team photos** - Professional headshots

## 🔧 Configuration

### Database Settings (`config.php`)
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'monolith_design');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
```

### Site Settings
```php
define('SITE_NAME', 'Your Company Name');
define('SITE_TAGLINE', 'Your Tagline');
define('SITE_URL', 'https://yourwebsite.com');
```

### Upload Settings
```php
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
```

## 📱 Responsive Design

The website is fully responsive with breakpoints at:
- **Desktop**: 1200px+ (full layout)
- **Tablet**: 768px-1199px (adapted layout)
- **Mobile**: 480px-767px (mobile layout)
- **Small Mobile**: <480px (compact layout)

## 🔒 Security Features

- **SQL Injection Protection** - Prepared statements
- **XSS Prevention** - Input sanitization
- **CSRF Protection** - Token-based forms
- **File Upload Validation** - Type and size restrictions
- **Secure Sessions** - HTTP-only cookies
- **Directory Protection** - .htaccess security headers

## 🌐 SEO Optimization

- **Clean URLs** - `/services/architectural-design/`
- **Meta Tags** - Title, description, keywords
- **Open Graph** - Social media sharing
- **Structured Data** - Schema.org markup
- **Image Optimization** - Alt tags and lazy loading
- **Page Speed** - Compressed assets and caching

## 📊 Performance

- **Optimized CSS** - Organized with CSS variables
- **Minimal JavaScript** - Vanilla JS, no jQuery
- **Image Lazy Loading** - Improves page speed
- **Caching Headers** - Browser caching enabled
- **Compressed Assets** - Gzip compression

## 🎯 Browser Support

- **Chrome** 60+
- **Firefox** 60+
- **Safari** 12+
- **Edge** 79+
- **Mobile browsers** (iOS Safari, Chrome Mobile)

## 🚨 Troubleshooting

### Common Issues

**Database Connection Error**
- Check credentials in `config.php`
- Verify MySQL server is running
- Ensure database exists

**Images Not Loading**
- Check file permissions on `/assets/images/uploads/`
- Verify image paths in database
- Ensure uploaded files are within size limits

**Clean URLs Not Working**
- Enable Apache mod_rewrite
- Check .htaccess file permissions
- Verify AllowOverride is enabled

**Mobile Menu Not Working**
- Check JavaScript console for errors
- Ensure main.js is loading properly
- Verify mobile breakpoint CSS

## 🔄 Updates

### Updating Content
1. Use the admin panel for most content updates
2. Images can be uploaded through the admin interface
3. Theme options can be changed without code modification

### Code Updates
1. Backup your database and files
2. Update core files (preserving config.php)
3. Run any database migrations if needed

## 📞 Support

For support with this theme:
1. Check this README file
2. Review the code comments
3. Test on a development environment first

## 📝 License

This theme is created for professional use. Customize and modify as needed for your projects.

---

**Monolith Design Co.** - Engineering the Future of Structures

Built with precision, designed for excellence. 🏗️
