/**
 * Arkify-Inspired Styles for Monolith Design Co.
 * Based on the Arkify template design
 */

/* CSS Variables for Arkify Design System */
:root {
    /* Colors - Updated to match admin config (Orange, Black, Blue) */
    --primary-color: #1A1A1A; /* Black from admin config */
    --secondary-color: #F5F5F5; /* Light gray background */
    --accent-color: #E67E22; /* Orange from admin config */
    --blue-accent: #3498DB; /* Blue accent color */
    --text-color: #1A1A1A;
    --text-light: #666666;
    --white: #FFFFFF;
    --black: #000000;
    --light-green: #F8FAF8;
    --border-color: #E5E5E5;
    
    /* Typography - Arkify uses Public Sans and Inter */
    --font-heading: 'Public Sans', sans-serif;
    --font-body: 'Inter', sans-serif;
    
    /* Spacing */
    --spacing-xs: 8px;
    --spacing-sm: 16px;
    --spacing-md: 24px;
    --spacing-lg: 32px;
    --spacing-xl: 48px;
    --spacing-xxl: 64px;
    --spacing-xxxl: 96px;
    
    /* Container */
    --container-max-width: 1200px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-medium: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Shadows */
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.2);
    
    /* Border Radius */
    --radius-small: 4px;
    --radius-medium: 8px;
    --radius-large: 16px;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-body);
    font-size: 16px;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    line-height: 1.1;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 600;
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
}

h4 {
    font-size: clamp(1.25rem, 2.5vw, 1.5rem);
}

h5 {
    font-size: clamp(1.125rem, 2vw, 1.25rem);
}

h6 {
    font-size: 1rem;
    font-weight: 600;
}

p {
    margin-bottom: var(--spacing-sm);
    color: var(--text-light);
}

/* Container */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-white {
    color: var(--white);
}

.bg-light-green {
    background-color: var(--light-green);
}

.bg-primary {
    background-color: var(--primary-color);
}

.d-none {
    display: none;
}

/* Buttons */
.primary-button {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    background-color: var(--primary-color);
    color: var(--white);
    text-decoration: none;
    border-radius: var(--radius-medium);
    font-weight: 500;
    transition: all var(--transition-medium);
    border: 2px solid var(--primary-color);
}

.primary-button:hover {
    background-color: transparent;
    color: var(--primary-color);
    transform: translateY(-2px);
}

.primary-button-dark {
    background-color: var(--white);
    color: var(--primary-color);
    border-color: var(--white);
}

.primary-button-dark:hover {
    background-color: transparent;
    color: var(--white);
    border-color: var(--white);
}

/* Hero Section */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--light-green) 0%, var(--white) 100%);
    position: relative;
}

.hero-outer {
    width: 100%;
}

.hero-wrap {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: clamp(3rem, 6vw, 5rem);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-color);
}

.hero-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-lg);
}

.hero-data {
    font-size: clamp(1.125rem, 2vw, 1.25rem);
    color: var(--text-light);
    max-width: 600px;
    line-height: 1.6;
}

.hero-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    text-decoration: none;
    transition: all var(--transition-medium);
    animation: bounce 2s infinite;
}

.hero-button:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* About Section */
.about-section {
    padding: var(--spacing-xxxl) 0;
}

.about-wrap {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;
}

.about-title {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.subtitle {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: var(--text-light);
}

.caption {
    color: var(--primary-color);
}

.about-cover,
.about-image {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-large);
}

.cover-image {
    width: 100%;
    height: auto;
    object-fit: cover;
    transition: transform var(--transition-slow);
}

.cover-image:hover {
    transform: scale(1.05);
}

.about-middle {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.about-info h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
    color: var(--text-color);
    margin-bottom: var(--spacing-md);
}

.about-info p {
    font-size: 1.125rem;
    line-height: 1.7;
}

/* Section Titles */
.section-title {
    margin-bottom: var(--spacing-xl);
}

.section-title .subtitle {
    margin-bottom: var(--spacing-sm);
}

.section-title h2 {
    max-width: 600px;
}

/* Featured Projects Section */
.featured-projects-section {
    padding: var(--spacing-xxxl) 0;
}

.work-outer-main {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.work-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.work-main-card {
    position: relative;
    border-radius: var(--radius-large);
    overflow: hidden;
    cursor: pointer;
}

.work-main {
    aspect-ratio: 16/10;
    overflow: hidden;
}

.work-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: var(--spacing-lg);
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: var(--white);
}

.work-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
}

.work-mini-card {
    background-color: rgba(255,255,255,0.2);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-small);
    backdrop-filter: blur(10px);
}

.body-small {
    font-size: 0.75rem;
    font-weight: 500;
}

.work-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.work-card {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-medium);
    transition: all var(--transition-medium);
    cursor: pointer;
}

.work-card:hover {
    background-color: var(--light-green);
    transform: translateY(-2px);
}

.work-cover {
    aspect-ratio: 4/3;
    border-radius: var(--radius-small);
    overflow: hidden;
}

.work-last {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background-color: var(--light-green);
    border-radius: var(--radius-large);
    margin-top: var(--spacing-lg);
}

.work-last p {
    max-width: 60%;
    margin: 0;
}

/* Statistics Section */
.statistics-section {
    padding: var(--spacing-xxxl) 0;
    color: var(--white);
}

.best-wrap {
    margin-bottom: var(--spacing-xxxl);
}

.subtitle-white {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.subtitle-white div {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: rgba(255,255,255,0.8);
}

.best-inner {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    text-align: center;
}

.best-card {
    padding: var(--spacing-lg);
}

.best-title {
    font-size: clamp(3rem, 6vw, 5rem);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    color: var(--white);
}

.body-large {
    font-size: 1.25rem;
    color: rgba(255,255,255,0.9);
}

/* Logo Carousel */
.gallery-outer {
    position: relative;
    overflow: hidden;
    padding: var(--spacing-lg) 0;
}

.left-linear,
.right-linear {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 100px;
    z-index: 2;
    pointer-events: none;
}

.left-linear {
    left: 0;
    background: linear-gradient(to right, var(--primary-color), transparent);
}

.right-linear {
    right: 0;
    background: linear-gradient(to left, var(--primary-color), transparent);
}

.gallery-wrap {
    display: flex;
    gap: var(--spacing-xl);
    animation: scroll-left 30s linear infinite;
}

.logo-card {
    flex-shrink: 0;
    width: 120px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255,255,255,0.1);
    border-radius: var(--radius-medium);
    backdrop-filter: blur(10px);
}

.logo-card img {
    max-width: 80px;
    max-height: 40px;
    filter: brightness(0) invert(1);
    opacity: 0.7;
    transition: opacity var(--transition-medium);
}

.logo-card:hover img {
    opacity: 1;
}

@keyframes scroll-left {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-100%);
    }
}

/* Services Section */
.services-section {
    padding: var(--spacing-xxxl) 0;
}

.service-block {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xxxl);
    align-items: center;
}

.project-title {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.service-left {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.service-mini-card {
    padding: var(--spacing-md);
    border: 1px solid rgba(45, 90, 39, 0.2);
    border-radius: var(--radius-medium);
    transition: all var(--transition-medium);
    cursor: pointer;
}

.service-mini-card:hover {
    background-color: var(--light-green);
    border-color: var(--primary-color);
    transform: translateX(10px);
}

.service-mini-card h5 {
    margin: 0;
    color: var(--text-color);
}

.service-image {
    position: relative;
    border-radius: var(--radius-large);
    overflow: hidden;
    aspect-ratio: 4/3;
}

/* Testimonials Section */
.testimonials-section {
    padding: var(--spacing-xxxl) 0;
    position: relative;
}

.testimonials-slider {
    position: relative;
}

.testimonial-slide {
    display: none;
}

.testimonial-slide.active {
    display: block;
}

.review-wrap {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-xl);
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;
}

.review-cover {
    aspect-ratio: 1;
    border-radius: var(--radius-large);
    overflow: hidden;
}

.review-inner {
    padding: var(--spacing-lg);
}

.review-info {
    font-size: 1.25rem;
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
    color: var(--text-color);
    font-style: italic;
}

.review-title h5 {
    margin-bottom: var(--spacing-xs);
    color: var(--text-color);
}

.review-title div {
    color: var(--text-light);
    font-size: 0.875rem;
}

.slider-navigation {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.left-arrow,
.right-arrow {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-medium);
}

.left-arrow:hover,
.right-arrow:hover {
    background-color: var(--text-color);
    transform: scale(1.1);
}

.slide-nav {
    display: flex;
    gap: var(--spacing-sm);
}

.slide-dot {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(45, 90, 39, 0.2);
    color: var(--text-light);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-medium);
    font-size: 0.875rem;
    font-weight: 500;
}

.slide-dot.active,
.slide-dot:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* Articles Section */
.articles-section {
    padding: var(--spacing-xxxl) 0;
}

.section-wrap {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.title-wrap {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

.articles-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
}

.article-main-card {
    position: relative;
    cursor: pointer;
}

.article-block {
    aspect-ratio: 16/10;
    border-radius: var(--radius-large);
    overflow: hidden;
    margin-bottom: var(--spacing-md);
}

.article-title {
    padding: var(--spacing-md);
}

.article-top {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
    font-size: 0.875rem;
    color: var(--text-light);
}

.article-line {
    width: 20px;
    height: 1px;
    background-color: var(--text-light);
}

.article-wrap {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.article-card {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-medium);
    transition: all var(--transition-medium);
    cursor: pointer;
    position: relative;
}

.article-card:hover {
    background-color: var(--light-green);
    transform: translateY(-2px);
}

.article-cover {
    aspect-ratio: 4/3;
    border-radius: var(--radius-small);
    overflow: hidden;
}

.article-link {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    text-decoration: none;
}

/* CTA Section */
.cta-section {
    padding: var(--spacing-xxxl) 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, #1a4a1a 100%);
    color: var(--white);
}

.cta-wrap {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.cta-outer {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    align-items: center;
}

.cta-title {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: center;
}

.cta-title .caption {
    font-size: 0.875rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: rgba(255,255,255,0.8);
}

.cta-info {
    font-size: 1.125rem;
    line-height: 1.6;
    color: rgba(255,255,255,0.9);
    max-width: 600px;
    margin: 0;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .about-wrap {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
        text-align: center;
    }

    .work-wrapper {
        grid-template-columns: 1fr;
    }

    .work-last {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .work-last p {
        max-width: 100%;
    }

    .service-block {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .review-wrap {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .articles-grid {
        grid-template-columns: 1fr;
    }

    .title-wrap {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: center;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-sm);
    }

    .hero-title {
        font-size: clamp(2rem, 8vw, 3rem);
    }

    .best-inner {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-lg);
    }

    .gallery-wrap {
        gap: var(--spacing-md);
    }

    .logo-card {
        width: 100px;
        height: 50px;
    }

    .work-card,
    .article-card {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .slider-navigation {
        flex-direction: column;
        gap: var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .best-inner {
        grid-template-columns: 1fr;
    }

    .hero-info {
        gap: var(--spacing-md);
    }

    .hero-data {
        font-size: 1rem;
    }

    .section-title h2 {
        font-size: clamp(1.5rem, 6vw, 2rem);
    }
}

/* Animation Classes */
.loading {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.loading.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Hover Effects */
.work-main-card:hover .cover-image,
.work-card:hover .cover-image,
.article-main-card:hover .cover-image,
.article-card:hover .cover-image {
    transform: scale(1.05);
}

/* Focus States */
.primary-button:focus,
.hero-button:focus,
.left-arrow:focus,
.right-arrow:focus,
.slide-dot:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* ===== ARKIFY HEADER STYLES ===== */
.arkify-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all 0.3s ease;
}

.header-wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) 0;
}

.header-logo .logo-link {
    display: flex;
    align-items: center;
}

.main-logo {
    height: 40px;
    width: auto;
}

.header-nav {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-list {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.nav-link {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-dropdown {
    position: relative;
}

.nav-dropdown-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    color: var(--text-color);
    font-weight: 500;
    font-size: 16px;
    cursor: pointer;
    transition: color 0.3s ease;
}

.nav-dropdown-btn:hover {
    color: var(--primary-color);
}

.nav-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: var(--spacing-sm);
    min-width: 160px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.nav-dropdown:hover .nav-dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: block;
    padding: var(--spacing-sm);
    color: var(--text-color);
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.dropdown-link:hover {
    background-color: var(--background-light);
}

.header-cta-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.header-cta-button:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger-line {
    width: 24px;
    height: 2px;
    background: var(--text-color);
    transition: all 0.3s ease;
}

.mobile-navigation {
    display: none;
    position: fixed;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid var(--border-color);
    z-index: 999;
}

.mobile-nav-content {
    padding: var(--spacing-lg);
}

.mobile-nav-menu {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.mobile-nav-link {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--border-color);
}

.mobile-cta-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    justify-content: center;
    margin-top: var(--spacing-md);
}

/* ===== ARKIFY FOOTER STYLES ===== */
.arkify-footer {
    background-color: var(--text-color);
    color: var(--white);
    padding: var(--spacing-xl) 0 var(--spacing-lg);
}

.footer-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.footer-main {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: var(--spacing-xl);
    align-items: start;
}

.footer-brand {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.footer-logo-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.footer-logo {
    height: 40px;
    width: auto;
}

.newsletter-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.newsletter-label {
    font-weight: 600;
    color: var(--white);
}

.newsletter-input-wrap {
    display: flex;
    gap: var(--spacing-xs);
}

.newsletter-input {
    flex: 1;
    padding: var(--spacing-sm);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
}

.newsletter-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.newsletter-submit {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
}

.newsletter-submit:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.footer-contact-info {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-lg);
}

.contact-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.contact-label {
    font-weight: 600;
    color: var(--white);
    font-size: 14px;
}

.contact-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
    line-height: 1.5;
}

.contact-links {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.contact-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.contact-link:hover {
    color: var(--white);
}

.footer-links {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.footer-nav-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.footer-nav-link:hover {
    color: var(--white);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-social {
    display: flex;
    gap: var(--spacing-md);
}

.social-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.social-link:hover {
    color: var(--white);
}

.footer-copyright {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.copyright-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 14px;
}

.footer-credits {
    color: rgba(255, 255, 255, 0.6);
    font-size: 12px;
}

.footer-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: var(--white);
}

/* ===== RESPONSIVE STYLES FOR HEADER & FOOTER ===== */
@media (max-width: 768px) {
    .header-nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .mobile-navigation.active {
        display: block;
    }

    .footer-main {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .footer-contact-info {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .footer-bottom {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .footer-social {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .header-wrap {
        padding: var(--spacing-sm) 0;
    }

    .main-logo {
        height: 32px;
    }

    .footer-links {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }

    .newsletter-input-wrap {
        flex-direction: column;
    }
}

/* Print Styles */
@media print {
    .hero-button,
    .slider-navigation,
    .gallery-outer {
        display: none;
    }

    .hero-section {
        min-height: auto;
        padding: var(--spacing-lg) 0;
    }

    * {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
    }
}
