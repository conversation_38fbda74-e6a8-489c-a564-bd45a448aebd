/**
 * Arkify-Inspired JavaScript for Monolith Design Co.
 * Handles animations, sliders, and interactive elements
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize all components
    initHeroSlider();
    initScrollAnimations();
    initTestimonialSlider();
    initSmoothScrolling();
    initLoadingAnimations();
    initHoverEffects();

    /**
     * Hero background slider
     */
    function initHeroSlider() {
        const heroSection = document.querySelector('.hero-section');
        const heroImages = document.querySelectorAll('.hero-background img');

        if (!heroSection || heroImages.length === 0) return;

        let currentIndex = 0;

        // Remove loading class after initial load
        setTimeout(() => {
            heroSection.classList.remove('loading');
        }, 1000);

        // Auto-slide functionality
        function nextSlide() {
            heroImages[currentIndex].classList.remove('active');
            currentIndex = (currentIndex + 1) % heroImages.length;
            heroImages[currentIndex].classList.add('active');
        }

        // Start auto-sliding after initial load
        setTimeout(() => {
            setInterval(nextSlide, 5000); // Change slide every 5 seconds
        }, 2000);
    }

    /**
     * Scroll-based animations
     */
    function initScrollAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('loaded');
                }
            });
        }, observerOptions);
        
        // Observe all elements with loading class
        document.querySelectorAll('.loading').forEach(el => {
            observer.observe(el);
        });
        
        // Observe sections for fade-in effects
        document.querySelectorAll('section').forEach(section => {
            section.classList.add('loading');
            observer.observe(section);
        });
    }
    
    /**
     * Testimonial slider functionality
     */
    function initTestimonialSlider() {
        const slides = document.querySelectorAll('.testimonial-slide');
        const dots = document.querySelectorAll('.slide-dot');
        const leftArrow = document.querySelector('.left-arrow');
        const rightArrow = document.querySelector('.right-arrow');
        
        if (slides.length === 0) return;
        
        let currentSlide = 0;
        
        function showSlide(index) {
            // Hide all slides
            slides.forEach(slide => slide.classList.remove('active'));
            dots.forEach(dot => dot.classList.remove('active'));
            
            // Show current slide
            if (slides[index]) {
                slides[index].classList.add('active');
            }
            if (dots[index]) {
                dots[index].classList.add('active');
            }
        }
        
        function nextSlide() {
            currentSlide = (currentSlide + 1) % slides.length;
            showSlide(currentSlide);
        }
        
        function prevSlide() {
            currentSlide = (currentSlide - 1 + slides.length) % slides.length;
            showSlide(currentSlide);
        }
        
        // Arrow navigation
        if (rightArrow) {
            rightArrow.addEventListener('click', nextSlide);
        }
        
        if (leftArrow) {
            leftArrow.addEventListener('click', prevSlide);
        }
        
        // Dot navigation
        dots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentSlide = index;
                showSlide(currentSlide);
            });
        });
        
        // Auto-play (optional)
        setInterval(nextSlide, 8000);
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowLeft') {
                prevSlide();
            } else if (e.key === 'ArrowRight') {
                nextSlide();
            }
        });
    }
    
    /**
     * Smooth scrolling for anchor links
     */
    function initSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }
    
    /**
     * Loading animations for cards and elements
     */
    function initLoadingAnimations() {
        // Stagger animation for cards
        const cardGroups = [
            '.best-inner .best-card',
            '.work-list .work-card',
            '.article-wrap .article-card',
            '.service-left .service-mini-card'
        ];
        
        cardGroups.forEach(selector => {
            const cards = document.querySelectorAll(selector);
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
            });
        });
    }
    
    /**
     * Hover effects and interactions
     */
    function initHoverEffects() {
        // Service cards hover effect
        document.querySelectorAll('.service-mini-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(10px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
            });
        });
        
        // Work cards hover effect
        document.querySelectorAll('.work-card, .article-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-4px)';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
        
        // Button hover effects
        document.querySelectorAll('.primary-button').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    }
    
    /**
     * Logo carousel animation control
     */
    function initLogoCarousel() {
        const carousels = document.querySelectorAll('.gallery-wrap');
        
        carousels.forEach(carousel => {
            carousel.addEventListener('mouseenter', () => {
                carousel.style.animationPlayState = 'paused';
            });
            
            carousel.addEventListener('mouseleave', () => {
                carousel.style.animationPlayState = 'running';
            });
        });
    }
    
    /**
     * Statistics counter animation
     */
    function initCounterAnimation() {
        const counters = document.querySelectorAll('.best-title');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counter = entry.target;
                    const target = parseInt(counter.textContent.replace(/\D/g, ''));
                    const suffix = counter.textContent.replace(/\d/g, '');
                    
                    animateCounter(counter, 0, target, suffix, 2000);
                    observer.unobserve(counter);
                }
            });
        });
        
        counters.forEach(counter => observer.observe(counter));
    }
    
    function animateCounter(element, start, end, suffix, duration) {
        const startTime = performance.now();
        
        function updateCounter(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const current = Math.floor(start + (end - start) * easeOutQuart(progress));
            element.textContent = current + suffix;
            
            if (progress < 1) {
                requestAnimationFrame(updateCounter);
            }
        }
        
        requestAnimationFrame(updateCounter);
    }
    
    function easeOutQuart(t) {
        return 1 - Math.pow(1 - t, 4);
    }
    
    // Initialize additional components
    initLogoCarousel();
    initCounterAnimation();
    
    /**
     * Parallax effect for hero section
     */
    function initParallaxEffect() {
        const hero = document.querySelector('.hero-section');
        if (!hero) return;
        
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            hero.style.transform = `translateY(${rate}px)`;
        });
    }
    
    // Initialize parallax (optional - can be resource intensive)
    // initParallaxEffect();
    
    /**
     * Mobile menu toggle (if needed)
     */
    function initMobileMenu() {
        const menuToggle = document.querySelector('.mobile-menu-toggle');
        const mobileMenu = document.querySelector('.mobile-menu');
        
        if (menuToggle && mobileMenu) {
            menuToggle.addEventListener('click', () => {
                mobileMenu.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });
        }
    }
    
    initMobileMenu();
    
    // Add loading class to body when page is fully loaded
    window.addEventListener('load', () => {
        document.body.classList.add('loaded');
    });
    
    // Service Image Switcher
    function initializeServiceSwitcher() {
        const serviceCards = document.querySelectorAll('.service-mini-card');
        const serviceImage = document.getElementById('service-image');

        // Define service images
        const serviceImages = {
            'architectural': 'images/demo-image/demo-images/imgi_18_6784f188034623b1f2de81a2_service.jpg',
            'structural': 'images/demo-image/demo-images/imgi_40_678b4fa8626623b854fc160e_project-main01-p-800.jpg',
            'construction': 'images/demo-image/demo-images/imgi_9_678b510a26ec52b2b74c5dc8_project-thumb02.jpg',
            'sustainable': 'images/demo-image/demo-images/imgi_10_678b51888f09cbd817b6271f_project-thumb06-1.jpg'
        };

        const serviceAlts = {
            'architectural': 'Architectural Design',
            'structural': 'Structural Engineering',
            'construction': 'Construction Management',
            'sustainable': 'Sustainable Design'
        };

        serviceCards.forEach(card => {
            card.addEventListener('click', function() {
                // Remove active class from all cards
                serviceCards.forEach(c => c.classList.remove('active'));

                // Add active class to clicked card
                this.classList.add('active');

                // Get service type
                const serviceType = this.getAttribute('data-service');

                // Update image if service type exists
                if (serviceImages[serviceType] && serviceImage) {
                    // Get the base URL from the current image src
                    const currentSrc = serviceImage.src;
                    const baseUrl = currentSrc.substring(0, currentSrc.lastIndexOf('/') + 1);

                    serviceImage.src = baseUrl + serviceImages[serviceType];
                    serviceImage.alt = serviceAlts[serviceType];
                }
            });
        });
    }

    // Initialize service switcher
    initializeServiceSwitcher();

    // Handle resize events
    let resizeTimer;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(() => {
            // Recalculate any size-dependent elements
            console.log('Window resized, recalculating layouts...');
        }, 250);
    });

});
